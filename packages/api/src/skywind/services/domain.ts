import * as Errors from "../errors";
import { sequelize as db } from "../storage/db";
import { FindOptions, ForeignKeyConstraintError, Transaction, UniqueConstraintError } from "sequelize";
import { DOMAIN_TYPE, GAME_TYPES } from "../utils/common";
import { BaseEntity, ChildEntity } from "../entities/entity";
import { getEntityDomainService } from "./entityDomainService";
import { DomainRouting } from "@skywind-group/sw-domain-routing";
import { getGameClientVersionService } from "./gameVersionService";
import { EntityGame, Game } from "../entities/game";
import { UrlPlaceholders } from "./gameUrl/urlPlaceholders";
import config from "../config";
import { Models } from "../models/models";
import { Domain, DynamicDomain, StaticDomain } from "../entities/domain";
import { EntityStaticDomainPoolService } from "./entityStaticDomainPool";
import * as DynamicDomainCache from "../cache/dynamicDomainCache";
import * as StaticDomainCache from "../cache/staticDomainCache";

const DynamicDomainModel = Models.DynamicDomainModel;
const StaticDomainModel = Models.StaticDomainModel;
const EntityModel = Models.EntityModel;

export const getDomainService =
    (type: DOMAIN_TYPE = DOMAIN_TYPE.DYNAMIC): DomainService<DynamicDomain | StaticDomain> => {

        if (type === DOMAIN_TYPE.DYNAMIC) {
            return new DynamicDomainService();
        } else {
            return new StaticDomainService();
        }
    };

export interface EntityHolder {
    entity?: BaseEntity;
}

export interface DomainService<T extends Domain> {
    create(data: T): Promise<T>;

    update(data: T): Promise<T>;

    findOne(id: number, transaction?: Transaction, lock?: string): Promise<T>;

    findAll(findOptions?: FindOptions<any> & EntityHolder): Promise<T[]>;

    remove(id: number): Promise<void>;
}

class DynamicDomainService implements DomainService<DynamicDomain> {

    private static domainRouting = new DomainRouting();

    public async create(data: DynamicDomain): Promise<DynamicDomain> {
        const domain: DynamicDomain = {
            domain: data.domain,
            environment: data.environment,
            description: data.description,
            provider: data.provider,
            status: data.status,
            expiryDate: data.expiryDate,
        };

        try {
            const created = await DynamicDomainModel.create(domain);

            await DynamicDomainService.domainRouting.findAndUpdateExistingDomains();

            return created.toInfo();
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new Errors.DynamicDomainAlreadyExists());
            }
            return Promise.reject(err);
        }
    }

    public async update(data: DynamicDomain): Promise<DynamicDomain> {
        return db.transaction(async (transaction) => {
            const domain = await DynamicDomainModel.findByPk(
                data.id,
                { transaction, lock: transaction.LOCK.UPDATE }
            );

            if (!domain) {
                return Promise.reject(new Errors.DomainNotFoundError());
            }

            if (data.domain) {
                domain.domain = data.domain;
            }

            if (data.environment) {
                const entity = await EntityModel.findOne({ where: { dynamicDomainId: data.id }, transaction });
                if (entity) {
                    return Promise.reject(new Errors.DomainInUseError());
                }
                domain.environment = data.environment;
            }

            if (data.description !== undefined) {
                domain.description = data.description;
            }

            if (data.provider !== undefined) {
                domain.provider = data.provider;
            }

            if (data.status !== undefined) {
                domain.status = data.status;
            }

            if (data.expiryDate !== undefined) {
                domain.expiryDate = data.expiryDate;
            }

            await domain.save({ transaction });
            await DynamicDomainService.domainRouting.findAndUpdateExistingDomains();

            DynamicDomainCache.reset(data.id);

            return domain.toInfo();
        });
    }

    public async remove(id: number): Promise<void> {
        let destroyed;
        try {
            destroyed = await DynamicDomainModel.destroy({ where: { id } });
            await DynamicDomainService.domainRouting.findAndUpdateExistingDomains();

            DynamicDomainCache.reset(id);
        } catch (err) {
            if (err instanceof ForeignKeyConstraintError) {
                return Promise.reject(new Errors.DomainInUseError());
            }
            return Promise.reject(err);
        }
        if (!destroyed) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }
    }

    public async findOne(id: number, transaction?: Transaction, lock?: any): Promise<DynamicDomain> {
        const domain = await DynamicDomainModel.findByPk(id, { transaction, lock });
        if (!domain) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }
        return domain.toInfo();
    }

    public async findAll(findOptions?: FindOptions<any> & EntityHolder): Promise<DynamicDomain[]> {
        const domains = await DynamicDomainModel.findAll(findOptions);
        return domains.map(v => v.toInfo());
    }
}

class StaticDomainService implements DomainService<StaticDomain> {

    public async create(data: StaticDomain): Promise<StaticDomain> {
        const domain: StaticDomain = {
            domain: data.domain,
            description: data.description,
            provider: data.provider,
            status: data.status,
            expiryDate: data.expiryDate,
        };
        const created = await StaticDomainModel.create(domain);
        return created.toInfo();
    }

    public async update(data: StaticDomain): Promise<StaticDomain> {
        const domain = await StaticDomainModel.findOne({ where: { id: data.id } });
        if (!domain) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }

        if (data.domain) {
            domain.domain = data.domain;
        }

        if (data.description !== undefined) {
            domain.description = data.description;
        }

        if (data.provider !== undefined) {
            domain.provider = data.provider;
        }

        if (data.status !== undefined) {
            domain.status = data.status;
        }

        if (data.expiryDate !== undefined) {
            domain.expiryDate = data.expiryDate;
        }

        await domain.save();

        StaticDomainCache.reset(data.id);

        return domain.toInfo();
    }

    public async findOne(id: number): Promise<StaticDomain> {
        const domain = await StaticDomainCache.findOne(id);
        if (!domain) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }

        return domain.toInfo();
    }

    public async findAll(findOptions: FindOptions<any> & EntityHolder = {}): Promise<StaticDomain[]> {
        const { entity, ...options } = findOptions;
        const domains = await StaticDomainModel.findAll(options);
        return domains.filter(v => !entity || entity.validateStaticDomain(v.domain)).map(v => v.toInfo());
    }

    public async remove(id: number): Promise<void> {
        let destroyed;

        try {
            destroyed = await StaticDomainModel.destroy({ where: { id } });

            StaticDomainCache.reset(id);
        } catch (err) {
            if (err instanceof ForeignKeyConstraintError) {
                return Promise.reject(new Errors.DomainInUseError());
            }
            return Promise.reject(err);
        }

        if (!destroyed) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }
    }
}

export function isItgMiniGsGame(game: Game) {
    return game.code.startsWith(config.itgGameCodePrefix) && game.type !== GAME_TYPES.external;
}

export async function buildHistoryUrl(url: string, entity: BaseEntity, game?: Game): Promise<string> {
    if (game && isItgMiniGsGame(game)) {
        return buildItgHistoryUrl(game, entity);
    }
    return buildSwHistoryUrl(url, entity, game);
}

export async function buildSwHistoryUrl(url, entity: BaseEntity, game?: Game): Promise<string> {
    if (!url || !url.match(UrlPlaceholders.STATIC_DOMAIN)) {
        return url;
    }

    const entityStaticDomain = await getEntityStaticDomain(entity);
    if (!entityStaticDomain) {
        return Promise.reject(new Errors.StaticDomainNotDefined());
    }

    url = url.replace(UrlPlaceholders.STATIC_DOMAIN, entityStaticDomain?.domain);

    if (url.includes(UrlPlaceholders.KEY_CLIENT_VERSION) && game) {
        const version = await getGameClientVersionService().getGameClientVersion(entity, { game } as EntityGame);
        url = url.replace(UrlPlaceholders.CLIENT_VERSION, version);
    }

    return url;
}

async function getEntityStaticDomain(entity: BaseEntity): Promise<StaticDomain> {
    const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
    const pickedStaticDomain = await entityStaticDomainPoolService.pickStaticDomain();
    return pickedStaticDomain || await getEntityDomainService(DOMAIN_TYPE.STATIC).get(entity);
}

export function buildItgHistoryUrl(game: Game, entity: BaseEntity): string {
    const lang = (entity as ChildEntity).defaultLanguage;
    return `${config.itgBaseUrl}/history/${game.providerGameCode}/?presenter=1&locale=${lang}`;
}
