import {
    ACTION_METHOD,
    COUNTRIES,
    DEFAULT_PASSWORD_PATTERN,
    DOMAIN_PATTERN,
    DOMAIN_PROVIDER,
    DOMAIN_STATUS,
    ENTITY_GAME_STATUS,
    GAME_CATEGORY_ITEM_TYPE,
    GAME_CATEGORY_TYPE,
    GAME_TYPES,
    HISTORY_RENDER_TYPE,
    LABEL_GROUPS_RELATIONS_TYPES,
    LABEL_GROUPS_TYPES,
    LANGUAGES,
    TIMESTAMP_PATTERN,
    TWO_FA_TYPE_ARR
} from "../../utils/common";
import { PROMO_OWNER, PROMO_REWARD_INTERVAL_TYPES, PROMO_STATE, PROMO_STATUS } from "../../entities/promotion";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { UserType } from "../../entities/user";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { UrlPlaceholders } from "../../services/gameUrl/urlPlaceholders";
import config from "../../config";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { AllowedJackpotConfigurationLevel } from "../../entities/jurisdiction";
import { isEmail } from "validator";
import { decodeId } from "../../utils/publicid";
import { EntityStatus } from "../../entities/entity";
import { isValidUrl } from "../../utils/validateUrl";
import { GameLimitsPermissionType } from "../../entities/schemaDefinition";

const expressValidator = require("express-validator");
const playerPasswordValidator = new RegExp(DEFAULT_PASSWORD_PATTERN);
const timestampRegexp = new RegExp(TIMESTAMP_PATTERN);
const POSSIBLE_SORT_ORDER = ["ASC", "DESC"];
const POSSIBLE_STATUS = Object.keys(ENTITY_GAME_STATUS).map(k => ENTITY_GAME_STATUS[k]);

const permissibleValue = (...permissibleValues: any[]) => {
    return (value) => {
        return permissibleValues.includes(value);
    };
};

function validateGameCategoryItem(item) {
    if (typeof item.type !== "string") {
        return false;
    }

    if (item.type.toLowerCase() === GAME_CATEGORY_ITEM_TYPE.INTERSECTION) {
        if (!Array.isArray(item.items)) {
            return false;
        }

        return item.items.every(c => validateGameCategoryItem(c));
    }

    if (!item.id) {
        return false;
    }

    if (item.type.toLowerCase() !== GAME_CATEGORY_ITEM_TYPE.GAME) {
        const value = decodeId(item.id);
        if (!Number.isFinite(value)) {
            return false;
        }
    }

    return true;
}

function isPromoIntervalOrNull(value: string) {
    if (!value) {
        return true;
    }
    return PROMO_REWARD_INTERVAL_TYPES.includes(value);
}

function isMultipleValide(receivedValues: string[], validateArray: string[]) {
    for (const receivedValue of receivedValues) {
        if (!validateArray.includes(receivedValue)) {
            return false;
        }
    }
    return true;
}

export const customValidators = {
    isWord: value => {
        return typeof value === "string" && /^[\w-]+$/.test(value);
    },
    isPassword: (value, options: { pattern: { test: (value: string) => boolean }, empty: boolean }) => {
        if (options && options.empty &&
            (!value || value.length === 0 || (typeof value === "string" && !value.trim()))) {
            return true;
        }
        if (options && options.pattern) {
            return options.pattern.test(value);
        }
        return playerPasswordValidator.test(value);
    },
    isStatus: value => POSSIBLE_STATUS.includes(value),
    isAvailableEntityStatus: value => Object.values(EntityStatus).includes(value),
    isSortOrder: value => POSSIBLE_SORT_ORDER.includes(value),
    isLabelGroup: value => {
        if (typeof value.group !== "string") {
            return false;
        }
        if (!isMultipleValide(value.type.split(","),
            [LABEL_GROUPS_TYPES.GAME, LABEL_GROUPS_TYPES.ENTITY, LABEL_GROUPS_TYPES.PROMOTION])) {
            return false;
        }
        return isMultipleValide(value.relationType.split(","),
            [LABEL_GROUPS_RELATIONS_TYPES.ONE_TO_ONE, LABEL_GROUPS_RELATIONS_TYPES.ONE_TO_MANY]);
    },
    isCommaSeparatedIntegers: (value, options?: { isEmptyStringAllowed: boolean }) => {
        if (options && !options.isEmptyStringAllowed && value === "") {
            return false;
        }
        return /^(\d+(,\d+)*)?$/.test(value);
    },
    isCommaSeparatedCurrencies: value => {
        return /^([A-Z]{3}(,[A-Z]{3})*)?$/.test(value);
    },
    isCurrency: value => {
        return Currencies.exists(value);
    },
    isPlayerCode: value => {
        return /^[\w-]{3,64}$/.test(value);
    },
    isPlayerCodeFromOperator: value => {
        return /^[\w-.]{3,64}$/.test(value);
    },
    isMerchantPlayerCode: value => {
        return !/[:\\]/.test(value);
    },
    isTimestampIso8601: value => {
        // validation regexp from https://gist.github.com/philipashlock/8830168
        return timestampRegexp.test(value) && !Number.isNaN(Date.parse(value));
    },
    isPromoStatus: value => {
        return value === PROMO_STATUS.ACTIVE || value === PROMO_STATUS.INACTIVE;
    },
    isPromoState: value => Object.values(PROMO_STATE).some(state => state === value),
    isPromoOwner: value => value === PROMO_OWNER.OPERATOR || value === PROMO_OWNER.SKYWIND,
    isPromoRewardInterval: (rewards) => {
        if (!isIterable(rewards)) {
            return false;
        }
        for (const reward of rewards) {
            const passed = isPromoIntervalOrNull(reward.intervalType) &&
                isPromoIntervalOrNull(reward.expirationPeriodType) &&
                isPromoIntervalOrNull(reward.payoutLimitPeriodType);
            if (!passed) {
                return false;
            }
        }
        return true;
    },
    isPromoInterval: (interval) => {
        return isPromoIntervalOrNull(interval);
    },
    isPromoType: value => {
        return value === PROMO_TYPE.FREEBET || value === PROMO_TYPE.BONUS_COIN;
    },
    isArray: function (value, options) {
        if (!Array.isArray(value)) {
            return false;
        }
        if (options && options.notEmpty && value.length === 0) {
            return false;
        }
        return true;
    },
    isMaxLength: (value, options) => {
        if (Array.isArray(value) && options && options.max < value.length) {
            return false;
        }
        return true;
    },
    isCountryCode: function (value, options: { allowNull: boolean, isSingleValue: boolean }): boolean {
        if (value === null && options && options.allowNull) {
            return true;
        }

        if (options && options.isSingleValue) {
            return !!COUNTRIES[value];
        }

        if (!Array.isArray(value)) {
            return false;
        }

        for (const item of value) {
            if (typeof item !== "string" || !COUNTRIES[item]) {
                return false;
            }
        }
        return true;
    },
    isAllowedJackpotConfigurationLevel: function (value, options: { allowNull: boolean }) {
        if (value === null && options && options.allowNull) {
            return true;
        }
        return Object.values(AllowedJackpotConfigurationLevel).includes(value);
    },
    isCurrencyCode: function (value) {
        if (value instanceof Array) {
            for (const item of value) {
                if (typeof item !== "string" || !Currencies.exists(item)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    },
    isLanguageCode: function (value) {
        if (value instanceof Array) {
            value.forEach(item => {
                if (typeof item !== "string" || !LANGUAGES[item]) {
                    return false;
                }
            });
            return true;
        }
        return false;
    },
    isNumericArray: (value, options = { notEmpty: false }) => {
        if (Array.isArray(value)) {
            if (!value.length && options.notEmpty) {
                return false;
            }

            for (const v of value) {
                if (!Number.isFinite(v)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    },
    isCSVFormat: value => {
        return value === "csv";
    },
    isAuthPhoneNumber: value => {
        return value === null || /^[\\+]?[0-9]{4,18}$/.test(value);
    },
    isTwoFAType: value => {
        return TWO_FA_TYPE_ARR.indexOf(value) !== -1;
    },
    isDebitOrCredit: value => {
        return value === "credit" || value === "debit";
    },
    isHistoryRenderType: permissibleValue(
        HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
        HISTORY_RENDER_TYPE.CANVAS_AND_HISTORY_FROM_GAME,
        HISTORY_RENDER_TYPE.CANVAS_AND_HISTORY_FROM_API,
        HISTORY_RENDER_TYPE.GAME_HISTORY_2
    ),
    isGameUrl: value => {
        if (!value) {
            return false;
        }
        let gameUrl = value;
        const options = {
            allow_underscores: true
        };
        if (UrlPlaceholders.STATIC_DOMAIN.test(gameUrl)) {
            gameUrl = value.replace(UrlPlaceholders.STATIC_DOMAIN, "domain.placeholder");
            options["host_whitelist"] = ["domain.placeholder"];
        }
        return expressValidator.validator.isURL(gameUrl, options);
    },
    isCommaSeparatedPermissibleFields: (commaSeparatedFields: string,
                                        options: { fields: string[] }) => {
        if (!options.fields || !commaSeparatedFields) {
            return true;
        }

        return commaSeparatedFields.split(",").every(field => options.fields.includes(field.trim()));

    },
    isString: (value, options) => {
        if (typeof value !== "string") {
            return false;
        }
        if (options) {
            if (options.min !== undefined && value.length < options.min) {
                return false;
            }
            if (options.max !== undefined && value.length > options.max) {
                return false;
            }
        }
        return true;
    },
    isDomain: value => {
        return DOMAIN_PATTERN.test(value);
    },
    isLobbyDomainTemplate: value => {
        if (typeof value !== "string") {
            return false;
        }
        if (!value.startsWith("-") && !value.startsWith(".")) {
            return false;
        }
        return DOMAIN_PATTERN.test(`lobby-id${value}`);
    },
    isDomainArray: items => {
        if (!Array.isArray(items)) {
            return false;
        }

        return items.every(item => typeof item === "string" && DOMAIN_PATTERN.test(item));
    },
    isDomainPoolItemArray: items => {
        if (!Array.isArray(items)) {
            return false;
        }

        return items.every(
            item => typeof item === "object"
                && item.id
                && (typeof item.id === "number" || typeof item.id === "string")
                && (!item.isActive || typeof item.isActive === "boolean")
        );
    },
    isDomainProvider: (value) => {
        return Object.values(DOMAIN_PROVIDER).includes(value);
    },
    isDomainStatus: (value) => {
        return Object.values(DOMAIN_STATUS).includes(value);
    },

    isGameCategoryItems: items => {
        if (!Array.isArray(items)) {
            return false;
        }

        return items.every(item => validateGameCategoryItem(item));
    },
    isInt: (value, options) => {
        const valueToNumber = typeof value === "string" && !(options && options.strict) ? +value : value;
        if (!Number.isInteger(valueToNumber)) {
            return false;
        }
        if (options) {
            if (options.min !== undefined && valueToNumber < options.min) {
                return false;
            }
            if (options.max !== undefined && valueToNumber > options.max) {
                return false;
            }
        }
        return true;
    },
    isFloat: (value, options) => {
        const valueToNumber = typeof value === "string" && !(options && options.strict)
            ? +value : value;
        if (Number.isNaN(valueToNumber) || !Number.isFinite(valueToNumber)) {
            return false;
        }
        if (options) {
            if (options.min !== undefined && valueToNumber < options.min) {
                return false;
            }
            if (options.max !== undefined && valueToNumber > options.max) {
                return false;
            }
        }
        return true;
    },
    isPositive: (value) => {
        const valueToNumber = +value;
        return valueToNumber > 0;
    },
    isPlayMode: (value) => {
        return Object.values(PlayMode).includes(value);
    },
    isGameCategoryType: (type) => {
        return type && Object.values(GAME_CATEGORY_TYPE).includes(type);
    },
    isLanguage(value) {
        return /^[a-z]{2}(-[a-z]{2})?$/.test(value);
    },
    isValidUrl: isValidUrl,
    isOptionalUrl: (url) => {
        if (typeof url !== "string") {
            return false;
        }
        if (url.trim() === "") {
            return true;
        }
        if (/[\s<>]/.test(url)) {
            return false;
        }

        let split = url.split("#", 2);
        url = split.shift();

        split = url.split("?", 2);
        url = split.shift();

        split = url.split("://", 2);
        if (split.length <= 1) {
            return false;
        }
        const protocol = split.shift().toLowerCase();
        if (["http", "https"].indexOf(protocol) === -1) {
            return false;
        }
        url = split.join("://");

        if (url === "") {
            return false;
        }

        split = url.split("/", 2);
        url = split.shift();

        let host;
        let port;
        let portValue = null;
        const ipv6Match = url.match(/^\[([^\]]+)\](?::([0-9]+))?$/);
        if (ipv6Match) {
            host = "";
            portValue = ipv6Match[2] || null;
        } else {
            split = url.split(":");
            host = split.shift();
            if (split.length) {
                portValue = split.join(":");
            }
        }

        if (portValue !== null && portValue.length > 0) {
            port = parseInt(portValue, 10);
            if (!/^[0-9]+$/.test(portValue) || port <= 0 || port > 65535) {
                return false;
            }
        }

        if (ipv6Match) {
            return true;
        }

        const parts = host.split(".");
        const tld = parts[parts.length - 1];
        // disallow spaces
        if (/\s/.test(tld)) {
            return false;
        }

        return parts.every((part) => {
            if (part.length > 63) {
                return false;
            }
            if (!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(part)) {
                return false;
            }
            // disallow full-width chars
            if (/[\uff01-\uff5e]/.test(part)) {
                return false;
            }
            // disallow parts starting or ending with hyphen
            return !/^-|-$/.test(part);
        });
    },
    isTimezoneName: (tz) => {
        try {
            new Date().toLocaleDateString("en", { timeZone: tz });
            return true;
        } catch (ex) {
            return false;
        }
    },

    isEmailArray: function (value, options) {
        if (!Array.isArray(value)) {
            return false;
        }
        if (options && options.notEmpty && value.length === 0) {
            return false;
        }

        return value.every(item => isEmail(item));
    },

    isCsvIntArray: function (value: string, options): boolean {
        if (!customValidators.isCommaSeparatedIntegers(value)) {
            return false;
        }
        for (const each of value.split(",")) {
            if (!customValidators.isInt(each, options)) {
                return false;
            }
        }
        return true;
    },

    isRecoveryType: value => {
        return value === "force-finish" || value === "revert" || value === "finalize";
    },

    isCommaSeparatedRecoveryType: commaSeparatedRecoveryTypes => {
        return commaSeparatedRecoveryTypes.split(",").every(
            recoveryType => customValidators.isRecoveryType(recoveryType));
    },

    isGameLimitPermission: value => !!GameLimitsPermissionType[value],

    hasUserType: value => Object.values(UserType).includes(value),

    isRegulation: value => config.regulations.includes(value),

    isTimeIntervalType: (intervalType) => {
        return ["minutely", "hourly", "daily", "weekly", "monthly", "yearly"].includes(intervalType);
    },
    isGameType: value => Object.values(GAME_TYPES).includes(value),

    nonSqlString: value => {
        return /^[\w-@._\s]+$/.test(value);
    },

    nonSqlStringCommaSeparated: value => {
        return /^[\w-@._\s,]+$/.test(value);
    },

    isAuditsSummaryMethod: (commaSeparatedValues) => {
        const values = commaSeparatedValues.split(",");

        for (const value of values) {
            if (!Object.values(ACTION_METHOD).includes(value)) {
                return false;
            }
        }

        return true;
    },
    hasSocketVersion: (value: string) => !!config.socket[value],
};

const isIterable = obj => obj && typeof obj[Symbol.iterator] === "function";

const validator = expressValidator({
    customValidators,
    customSanitizers: {
        toLowerCase: (value) => {
            if (typeof value === "string") {
                return value.toLowerCase();
            }
            return value;
        },
        toCommaSeparatedStringToArray: (value) => {
            if (typeof value === "string") {
                return value.toString().split(",").map(name => name.trim());
            }

            return value;

        }
    }
});

export default validator;
