import { mergeWith } from "lodash";

// validate that password is longer than or equal 8 letters, is no longer than or equal 255 and
// contains at least one letter, one uppercase letter and one digit
export const DEFAULT_PASSWORD_PATTERN
    = "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)^[a-zA-Z0-9!@#$%^&*()_\\~\\-\\`\\\\/\\\"\\'+|\\[\\]}{:;'?/>.<,]{8,255}$";

export const GAME_TYPES = {
    slot: "slot",
    action: "action",
    table: "table",
    external: "external",
    live: "live"
};

export const STAKED_GAMES_TYPES = [GAME_TYPES.slot, GAME_TYPES.external, GAME_TYPES.table, GAME_TYPES.live];

export const TWO_FA_TYPE = {
    SMS: "sms",
    EMAIL: "email",
    GOOGLE: "google"
};

export const TWO_FA_TYPE_ARR = [TWO_FA_TYPE.SMS, TWO_FA_TYPE.EMAIL, TWO_FA_TYPE.GOOGLE];

export enum HISTORY_RENDER_TYPE {
    OLD_AND_HISTORY_FROM_GAME,
    CANVAS_AND_HISTORY_FROM_API,
    CANVAS_AND_HISTORY_FROM_GAME,
    GAME_HISTORY_2
}

export const CANVAS_RENDER = [
    HISTORY_RENDER_TYPE.CANVAS_AND_HISTORY_FROM_API,
    HISTORY_RENDER_TYPE.CANVAS_AND_HISTORY_FROM_GAME
];

export const GAME_CATEGORY_ITEM_TYPE = {
    INTERSECTION: "intersection",
    GAME: "game",
    PROVIDER: "provider",
    LABEL: "label"
};

export const GAME_CATEGORY_TYPE = {
    GENERAL: "general",
    GAMESTORE: "gamestore"
};

export const SORT_ORDER = {
    DESC: "DESC",
    ASC: "ASC"
};

export const DOMAIN_PATTERN = /^[A-z0-9-_]+(\.[A-z0-9-_]+)*$/;

export const SUPER_ADMIN_ID = 1;
export const SUPER_ADMIN_ROLE_ID = 1;

export const MAX_BIGINT_VALUE_POSTGRES = 9223372036854775807;
export const MAX_INT_VALUE = MAX_BIGINT_VALUE_POSTGRES - Number.MAX_SAFE_INTEGER;

// validation regexp from https://gist.github.com/philipashlock/8830168
/*export const TIMESTAMP_PATTERN = "^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)" +
    "((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])" +
    "(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))" +
    "([T\\s]((([01]\\d|2[0-3])((:?)[0-5]\\d)?|24\\:?00)([\\.,]\\d+(?!:))?)?(\\17[0-5]\\d([\\.,]\\d+)?)?" +
    "([zZ]|([\\+-])([01]\\d|2[0-3]):?([0-5]\\d)?)?)?)?$";*/
export const TIMESTAMP_PATTERN = /^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;

export const PLAYER_CODE_MIN_LENGTH = 3;
export const PLAYER_CODE_MAX_LENGTH = 64;

export enum ENTITY_GAME_STATUS {
    SUSPENDED = "suspended",
    NORMAL = "normal",
    TEST = "test",
    HIDDEN = "hidden"
}

export const GROUP_ACTION_MAX_ITEMS = 100;

export enum DOMAIN_TYPE {
    DYNAMIC = "dynamic",
    STATIC = "static"
}

export enum DOMAIN_PROVIDER {
    GOOGLE = "google",
    CLOUDFLARE = "cloudflare",
    CLOUDFRONT = "cloudfront",
    CDNETWORKS = "cdnetworks",
    AKAMAI = "akamai",
    FASTLY = "fastly",
    OTHER = "other"
}

export enum DOMAIN_STATUS {
    ACTIVE = "active",
    SUSPENDED = "suspended"
}

export const COMMENT_MAX_LENGTH = 2048;

export const NICKNAME_MIN_LENGTH = 2;
export const NICKNAME_MAX_LENGTH = 15;

export enum LABEL_GROUPS_TYPES {
    GAME = "game",
    ENTITY = "entity",
    PROMOTION = "promotion",
}

export enum LABEL_GROUPS_RELATIONS_TYPES {
    ONE_TO_ONE = "o",
    ONE_TO_MANY = "m",
}

export const FORBIDDEN_CURRENCIES = ["XXX"];

export const HIDDEN_PASSWORD = "*****";
export const HIDDEN_ACCESS_TOKEN = "*****";

export const SUPERADMIN_USERNAME = "SUPERADMIN";
export const SUPERADMIN_PASSWORD = "SUPERadmin777";

export const VARCHAR_DEFAULT_LENGTH = 255;

export function filterObjectFields<T = object>(settings: T,
                                               filter: (value: any,
                                                        key: string) => boolean = (value) => value !== null): T {
    // By default removes properties with value equals null
    const cleanedSettings = {};

    for (const key of Object.keys(settings || {})) {
        if (filter(settings[key], key)) {
            if (settings[key] instanceof Object && !(settings[key] instanceof Array)) {
                cleanedSettings[key] = filterObjectFields(settings[key], filter);
            } else {
                cleanedSettings[key] = settings[key];
            }
        }
    }

    return cleanedSettings as T;
}

export function mergeArrayToObject(arrayToMerge: any[]) {
    const result = {};
    for (const valueToMerge of arrayToMerge) {
        if (valueToMerge) {
            mergeWith(result, valueToMerge, (objValue, newValue) => {
                if (Array.isArray(objValue)) {
                    return newValue;
                }
            });
        }
    }
    return Object.keys(result).length === 0 ? undefined : result;
}

/**
 *  Merges objects recursively. Each object is compared with another one in the same array.
 *  If there is matching objects are merged.
 */
export function mergeObjectArray(sourceArray: object[]): object[] {
    const result: object[] = [];

    if (!sourceArray.length) {
        return result;
    }
    for (const source of sourceArray) {
        if (!source) {
            continue;
        }
        const target = result.find(t => areObjectsMatch(t, source));

        if (target) {
            for (const key in target) {
                if (!target.hasOwnProperty(key) || !source.hasOwnProperty(key)) {
                    continue;
                }

                if (isObject(target[key]) && isObject(source[key])) {
                    target[key] = { ...target[key], ...source[key] };
                }
                if (Array.isArray(target[key]) && Array.isArray(source[key])) {
                    target[key] = mergeObjectArray([...target[key], ...source[key]]);
                }
            }
        } else {
            result.push(source);
        }
    }
    return result;
}

/**
 *  Compares objects by primitive properties.
 */
export function areObjectsMatch(target: object, source: object): boolean {
    if (!isObject(target) || !isObject(source)) {
        return false;
    }
    for (const key in target) {
        if (!target.hasOwnProperty(key) || !source.hasOwnProperty(key)) {
            continue;
        }
        if (!isPrimitive(target[key]) || !isPrimitive(source[key])) {
            continue;
        }
        if (target[key] !== source[key]) {
            return false;
        }
    }
    return true;
}

export function validatePositiveNumber(value: number): boolean {
    return Number.isFinite(value) && value > 0;
}

export const TIME_CONSTS = {
    MINUTE_TICKS: 60 * 1000,
    HOUR_TICKS: 60 * 60 * 1000,
    DAY_TICKS: 24 * 60 * 60 * 1000,
    WEEK_TICKS: 7 * 24 * 60 * 60 * 1000,
    MONTH_TICKS: 30 * 24 * 60 * 60 * 1000,
    YEAR_TICKS: 365 * 24 * 60 * 60 * 1000
};

export function isObject(item) {
    return typeof item === "object" && !Array.isArray(item) && item !== null;
}

export function isPrimitive(value) {
    const type = typeof value;
    return value == null || (type !== "object" && type !== "function");
}

export function mergeDeep(target, ...sources: object[]) {
    if (!sources.length) {
        return target;
    }
    const source = sources.shift();

    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key]) && source[key] !== undefined) {
                if (!target[key]) {
                    Object.assign(target, { [key]: {} });
                }
                mergeDeep(target[key], source[key]);
            } else if (source[key] !== undefined) {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }

    return mergeDeep(target, ...sources);
}

export function roundNumber(value: number) {
    return +value.toFixed(8);
}

export enum ACTION_METHOD {
    GET = "get",
    POST = "post",
    PUT = "put",
    PATCH = "patch",
    DELETE = "delete",
    CRON = "cron",
    SERVICE = "service",
}

export const X_ACCESS_TOKEN = "x-access-token";
export const X_OAUTH_TOKEN = "x-oauth-token";
export const X_TERMINAL_TOKEN = "x-terminal-token";
export const X_PLAYER_TOKEN = "x-player-token";

export const COUNTRIES = require("../../../resources/countries.json");
export const COUNTRIES_CODES = Object.keys(COUNTRIES);
export const LANGUAGES = require("../../../resources/languages.json");

// TODO: implement game images management in BO & do DB migration
export const GAME_IMAGES: Record<string, any> = require("../../../resources/game-images.json");

export const SOCKET_VERSION = {
    "2": "2",
    "4": "4"
}
