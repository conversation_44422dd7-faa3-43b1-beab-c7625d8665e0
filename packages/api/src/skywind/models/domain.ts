import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, StaticDomain } from "../entities/domain";
import { DOMAIN_PROVIDER, DOMAIN_STATUS } from "../utils/common";

export class DynamicDomainModel extends Model<
    InferAttributes<DynamicDomainModel>,
    InferCreationAttributes<DynamicDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare environment: string | null;
    declare description: CreationOptional<string>;
    declare provider: CreationOptional<DOMAIN_PROVIDER>;
    declare status: CreationOptional<DOMAIN_STATUS>;
    declare expiryDate: CreationOptional<Date>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain,
            environment: this.environment,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

DynamicDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: false },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true,
            defaultValue: DOMAIN_STATUS.ACTIVE
        },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "dynamic_domains",
        sequelize: db,
        underscored: true,
    }
);

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}

export class StaticDomainModel extends Model<
    InferAttributes<StaticDomainModel>,
    InferCreationAttributes<StaticDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare description: CreationOptional<string>;
    declare provider: CreationOptional<DOMAIN_PROVIDER>;
    declare status: CreationOptional<DOMAIN_STATUS>;
    declare expiryDate: CreationOptional<Date>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): StaticDomain {
        return {
            id: this.id,
            domain: this.domain,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

StaticDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true,
            defaultValue: DOMAIN_STATUS.ACTIVE
        },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "static_domains",
        sequelize: db,
    }
);

export function getStaticDomainModel() {
    return StaticDomainModel;
}
