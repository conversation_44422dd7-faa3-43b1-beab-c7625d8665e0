import { lazy } from "@skywind-group/sw-utils";
import { getDynamicDomainModel, getStaticDomainModel } from "../../skywind/models/domain";
import { DOMAIN_PROVIDER, DOMAIN_STATUS } from "../../skywind/utils/common";
import { FACTORY } from "./common";

const factory = require("factory-girl").factory;

interface DomainBuildOptions {
    domain?: string;
    description?: string;
    provider?: DOMAIN_PROVIDER;
    status?: DOMAIN_STATUS;
    expiryDate?: Date;
}

interface DynamicBuildOptions extends DomainBuildOptions {
    environment?: string;
}

export const defineDomainFactory = lazy(() => {
    factory.define(FACTORY.STATIC_DOMAIN, getStaticDomainModel(), (buildOptions: DomainBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            description: buildOptions.description || factory.chance("sentence"),
            provider: buildOptions.provider || factory.chance("pickone", Object.values(DOMAIN_PROVIDER)),
            status: buildOptions.status || factory.chance("pickone", Object.values(DOMAIN_STATUS)),
            expiryDate: buildOptions.expiryDate || factory.chance("date", { year: 2025 })
        };
    });

    return factory.define(FACTORY.DYNAMIC_DOMAIN, getDynamicDomainModel(), (buildOptions: DynamicBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            environment: buildOptions.environment || factory.chance("word"),
            description: buildOptions.description || factory.chance("sentence"),
            provider: buildOptions.provider || factory.chance("pickone", Object.values(DOMAIN_PROVIDER)),
            status: buildOptions.status || factory.chance("pickone", Object.values(DOMAIN_STATUS)),
            expiryDate: buildOptions.expiryDate || factory.chance("date", { year: 2025 })
        };
    });
});
